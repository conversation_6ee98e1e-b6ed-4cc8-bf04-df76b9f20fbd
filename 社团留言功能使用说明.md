# 社团留言功能使用说明

## 功能概述

社团留言功能提供了类似贴吧的交流体验，支持发表留言、回复留言、点赞等功能。

## 主要特性

- ✅ 发表主留言
- ✅ 多级回复（支持回复的回复）
- ✅ 点赞/取消点赞
- ✅ 留言置顶
- ✅ 留言删除
- ✅ 关键词搜索
- ✅ 分页查询
- ✅ 实时统计（点赞数、回复数）

## 重要变更说明

**根节点字段默认值调整**: 
- `parent_id` 和 `root_id` 字段默认值从 `NULL` 改为 `0`
- `parent_id = 0` 表示主留言
- `parent_id > 0` 表示回复留言
- `root_id = 0` 表示该留言本身就是根留言
- `root_id > 0` 表示该留言属于某个讨论串

## API 接口说明

### 1. 分页查询社团留言
**接口**: `POST /clubMessage/page`

**请求参数**:
```json
{
    "clubId": 1,                    // 社团ID（必填）
    "parentId": 0,                  // 父留言ID（0表示查询主留言，>0表示查询回复）
    "keyword": "关键词",             // 搜索关键词（可选）
    "messageType": 1,               // 留言类型（可选）
    "onlyPinned": false,            // 是否只查询置顶留言
    "includeReplies": true,         // 是否包含回复列表
    "replyLimit": 3,                // 回复列表限制数量
    "page": 1,                      // 页码
    "limit": 20,                    // 每页大小
    "orderBy": "created_at",        // 排序字段
    "orderDirection": "DESC"        // 排序方向
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "clubId": 1,
            "parentId": 0,
            "rootId": 0,
            "content": "欢迎大家加入我们的社团！",
            "authorIdCard": "320412199001011234",
            "authorInfo": {
                "name": "张三",
                "avatar": "..."
            },
            "likeCount": 5,
            "replyCount": 3,
            "isLiked": true,
            "isPinned": 1,
            "createdAt": "2024-01-01 10:00:00",
            "replies": [
                {
                    "id": 2,
                    "parentId": 1,
                    "rootId": 1,
                    "content": "谢谢社长的欢迎！",
                    "authorInfo": {...},
                    "isLiked": false
                }
            ]
        }
    ],
    "total": 50
}
```

### 2. 发表留言
**接口**: `POST /clubMessage/add`

**请求参数**:
```json
{
    "clubId": 1,                    // 社团ID（必填）
    "content": "这是一条新留言",      // 留言内容（必填）
    "messageType": 1,               // 留言类型（可选，默认为1）
    "parentId": 0,                  // 父留言ID（默认为0，表示主留言）
    "rootId": 0                     // 根留言ID（默认为0）
}
```

### 3. 回复留言
**接口**: `POST /clubMessage/reply`

**请求参数**:
```json
{
    "clubId": 1,                    // 社团ID（必填）
    "parentId": 1,                  // 父留言ID（必填，>0）
    "rootId": 1,                    // 根留言ID（必填，>0）
    "content": "这是一条回复",       // 回复内容（必填）
    "replyToIdCard": "320412199001011234"  // 回复目标用户（可选）
}
```

### 4. 点赞/取消点赞
**接口**: `POST /clubMessage/like`

**请求参数**:
```json
{
    "messageId": 1,                 // 留言ID（必填）
    "isLike": true                  // true=点赞，false=取消点赞
}
```

### 5. 删除留言
**接口**: `POST /clubMessage/delete`

**请求参数**:
```json
{
    "id": 1                         // 留言ID（必填）
}
```

### 6. 置顶留言
**接口**: `POST /clubMessage/pin`

**请求参数**:
```json
{
    "id": 1,                        // 留言ID（必填）
    "isPinned": 1                   // 1=置顶，0=取消置顶
}
```

### 7. 获取回复列表
**接口**: `POST /clubMessage/replies`

**请求参数**:
```json
{
    "parentId": 1,                  // 父留言ID（必填，>0）
    "page": 1,                      // 页码
    "limit": 20                     // 每页大小
}
```

### 8. 查询主留言列表
**接口**: `POST /clubMessage/mainMessages`

只返回主留言（parentId=0），适用于首页展示。

### 9. 查询置顶留言
**接口**: `POST /clubMessage/pinnedMessages`

只返回置顶的主留言。

## 数据库表结构

### police_club_message（社团留言表）
| 字段名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| id | BIGINT | AUTO_INCREMENT | 主键ID |
| club_id | BIGINT | - | 所属社团ID |
| parent_id | BIGINT | 0 | 父留言ID（0表示主留言） |
| root_id | BIGINT | 0 | 根留言ID（用于快速查找讨论串） |
| content | TEXT | - | 留言内容 |
| author_id_card | VARCHAR(18) | - | 留言作者身份证号 |
| reply_to_id_card | VARCHAR(18) | NULL | 回复目标用户身份证号 |
| message_type | TINYINT | 1 | 留言类型（1=普通，2=置顶，3=公告） |
| is_pinned | TINYINT | 0 | 是否置顶（0=否，1=是） |
| like_count | INT | 0 | 点赞数 |
| reply_count | INT | 0 | 回复数 |
| status | TINYINT | 1 | 状态（0=已删除，1=正常，2=待审核） |

### police_club_message_like（留言点赞表）
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| message_id | BIGINT | 留言ID |
| user_id_card | VARCHAR(18) | 点赞用户身份证号 |
| created_at | DATETIME | 点赞时间 |

## 使用示例

### 前端调用示例（JavaScript）

```javascript
// 1. 查询社团留言列表（主留言）
const getMainMessages = async (clubId, page = 1) => {
    const response = await fetch('/clubMessage/page', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            clubId: clubId,
            parentId: 0,  // 查询主留言
            page: page,
            limit: 20,
            includeReplies: true,
            replyLimit: 3
        })
    });
    return await response.json();
};

// 2. 发表主留言
const addMainMessage = async (clubId, content) => {
    const response = await fetch('/clubMessage/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            clubId: clubId,
            content: content,
            parentId: 0,  // 主留言
            rootId: 0     // 根留言
        })
    });
    return await response.json();
};

// 3. 回复留言
const replyMessage = async (clubId, parentId, rootId, content, replyToIdCard) => {
    const response = await fetch('/clubMessage/reply', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            clubId: clubId,
            parentId: parentId,  // 父留言ID（>0）
            rootId: rootId,      // 根留言ID（>0）
            content: content,
            replyToIdCard: replyToIdCard
        })
    });
    return await response.json();
};

// 4. 获取某条留言的回复列表
const getReplies = async (parentId, page = 1) => {
    const response = await fetch('/clubMessage/replies', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            parentId: parentId,  // 父留言ID（>0）
            page: page,
            limit: 20
        })
    });
    return await response.json();
};
```

## 注意事项

1. **字段默认值变更**: `parent_id` 和 `root_id` 现在默认为 0 而不是 NULL
2. **主留言判断**: `parent_id = 0` 表示主留言，`parent_id > 0` 表示回复
3. **根留言判断**: `root_id = 0` 表示该留言本身就是根留言
4. **回复层级**: 支持多级回复，通过 `parent_id` 和 `root_id` 维护层级关系
5. **数据一致性**: 点赞数和回复数通过冗余字段存储，提高查询性能
6. **权限控制**: 当前实现中，所有社团成员都可以发表留言和回复

## 扩展功能建议

1. **富文本支持**: 可扩展content字段支持富文本格式
2. **图片/文件上传**: 可添加附件字段支持多媒体内容
3. **@提醒功能**: 可扩展实现用户提醒功能
4. **举报功能**: 可添加举报表实现内容举报
5. **表情回应**: 可扩展点赞功能支持多种表情回应
