package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 民警标签信息修改DTO
 */
@Data
public class PoliceTagInfoXjdfUpdateDTO {

    /**
     * 要修改的记录ID列表
     */
    private List<Long> ids;

    /**
     * 标签类别
     */
    private String tagType;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 获得时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate awardDate;

    /**
     * 说明
     */
    private String remark;
}
