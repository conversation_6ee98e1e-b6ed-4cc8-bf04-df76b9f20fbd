package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.AuxiliaryPoliceInfoQueryDTO;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
import com.hl.archive.service.AuxiliaryPoliceInfoService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/auxiliaryPoliceInfo")
@RequiredArgsConstructor
@Api(tags = "辅警信息")
public class AuxiliaryPoliceInfoController {

    private final AuxiliaryPoliceInfoService auxiliaryPoliceInfoService;

    @PostMapping("/page")
    @ApiOperation("分页查询")
    public R<List<AuxiliaryPoliceInfo>> page(@RequestBody AuxiliaryPoliceInfoQueryDTO dto) {
        Page<AuxiliaryPoliceInfo> page = auxiliaryPoliceInfoService.pageList(dto);
        return R.ok(page.getRecords(), (int) page.getTotal());
    }

    @PostMapping("/add")
    @ApiOperation("添加")
    public R<Boolean> add(@RequestBody AuxiliaryPoliceInfo auxiliaryPoliceInfo) {
        auxiliaryPoliceInfo.setSourceType(1);
        return R.ok(auxiliaryPoliceInfoService.save(auxiliaryPoliceInfo));
    }

    @PostMapping("/update")
    @ApiOperation("更新")
    public R<Boolean> update(@RequestBody AuxiliaryPoliceInfo auxiliaryPoliceInfo) {
        auxiliaryPoliceInfo.setChangeStatus(1);
        return R.ok(auxiliaryPoliceInfoService.updateById(auxiliaryPoliceInfo));
    }

    @PostMapping("/delete")
    @ApiOperation("删除")
    public R<Boolean> delete(@RequestBody AuxiliaryPoliceInfo auxiliaryPoliceInfo) {
        AuxiliaryPoliceInfo infoServiceById = auxiliaryPoliceInfoService.getById(auxiliaryPoliceInfo.getId());
        if (infoServiceById.getSourceType() == 0) {
            return R.fail("非手动添加数据,不能删除");
        }
        return R.ok(auxiliaryPoliceInfoService.removeById(auxiliaryPoliceInfo));
    }
}
