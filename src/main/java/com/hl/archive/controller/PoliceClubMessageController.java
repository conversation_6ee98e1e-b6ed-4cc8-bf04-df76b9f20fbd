package com.hl.archive.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.MessageAddDTO;
import com.hl.archive.domain.dto.MessageLikeDTO;
import com.hl.archive.domain.dto.MessageQueryDTO;
import com.hl.archive.domain.entity.PoliceClubMessage;
import com.hl.archive.service.PoliceClubMessageService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 社团留言管理控制器
 */
@RestController
@RequestMapping("/clubMessage")
@RequiredArgsConstructor
@Slf4j
@Api(tags = "社团留言管理")
public class PoliceClubMessageController {

    private final PoliceClubMessageService policeClubMessageService;

    /**
     * 分页查询社团留言
     */
    @PostMapping("/page")
    @ApiOperation("分页查询社团留言")
    public R<List<PoliceClubMessage>> pageMessages(@RequestBody MessageQueryDTO dto) {
        try {
            // 设置当前用户身份证号，用于查询点赞状态
            if (dto.getCurrentUserIdCard() == null) {
                dto.setCurrentUserIdCard(UserUtils.getUser().getIdCard());
            }

            Page<PoliceClubMessage> page = policeClubMessageService.pageMessages(dto);
            return R.ok(page.getRecords(), (int) page.getTotal());
        } catch (Exception e) {
            log.error("分页查询社团留言失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 发表留言
     */
    @PostMapping("/add")
    @ApiOperation("发表留言")
    public R<Boolean> addMessage(@RequestBody MessageAddDTO dto) {
        try {
            // 设置留言作者为当前用户
            dto.setAuthorIdCard(UserUtils.getUser().getIdCard());
            
            boolean result = policeClubMessageService.addMessage(dto);
            return result ? R.ok(true) : R.fail("发表留言失败");
        } catch (Exception e) {
            log.error("发表留言失败", e);
            return R.fail("发表留言失败：" + e.getMessage());
        }
    }

    /**
     * 回复留言
     */
    @PostMapping("/reply")
    @ApiOperation("回复留言")
    public R<Boolean> replyMessage(@RequestBody MessageAddDTO dto) {
        try {
            // 设置留言作者为当前用户
            dto.setAuthorIdCard(UserUtils.getUser().getIdCard());
            
            boolean result = policeClubMessageService.replyMessage(dto);
            return result ? R.ok(true) : R.fail("回复留言失败");
        } catch (Exception e) {
            log.error("回复留言失败", e);
            return R.fail("回复留言失败：" + e.getMessage());
        }
    }

    /**
     * 获取留言的回复列表
     */
    @PostMapping("/replies")
    @ApiOperation("获取留言回复列表")
    public R<List<PoliceClubMessage>> getReplies(@RequestBody MessageQueryDTO dto) {
        try {
            // 设置当前用户身份证号，用于查询点赞状态
            if (dto.getCurrentUserIdCard() == null) {
                dto.setCurrentUserIdCard(UserUtils.getUser().getIdCard());
            }
            
            List<PoliceClubMessage> replies = policeClubMessageService.getReplies(dto);
            return R.ok(replies);
        } catch (Exception e) {
            log.error("获取留言回复列表失败", e);
            return R.fail("获取回复列表失败：" + e.getMessage());
        }
    }

    /**
     * 点赞/取消点赞留言
     */
    @PostMapping("/like")
    @ApiOperation("点赞留言")
    public R<Boolean> likeMessage(@RequestBody MessageLikeDTO dto) {
        try {
            // 设置点赞用户为当前用户
            dto.setUserIdCard(UserUtils.getUser().getIdCard());
            
            boolean result = policeClubMessageService.likeMessage(dto);
            return result ? R.ok(true) : R.fail("操作失败");
        } catch (Exception e) {
            log.error("点赞操作失败", e);
            return R.fail("点赞操作失败：" + e.getMessage());
        }
    }

    /**
     * 删除留言
     */
    @PostMapping("/delete")
    @ApiOperation("删除留言")
    public R<Boolean> deleteMessage(@RequestBody PoliceClubMessage request) {
        try {
            String operatorIdCard = UserUtils.getUser().getIdCard();
            boolean result = policeClubMessageService.deleteMessage(request.getId(), operatorIdCard);
            return result ? R.ok(true) : R.fail("删除留言失败");
        } catch (Exception e) {
            log.error("删除留言失败", e);
            return R.fail("删除留言失败：" + e.getMessage());
        }
    }

//    /**
//     * 置顶/取消置顶留言
//     */
//    @PostMapping("/pin")
//    @ApiOperation("置顶留言")
//    public R<Boolean> pinMessage(@RequestBody PoliceClubMessage request) {
//        try {
//            String operatorIdCard = UserUtils.getUser().getIdCard();
//            boolean isPinned = request.getIsPinned() != null && request.getIsPinned() == 1;
//            boolean result = policeClubMessageService.pinMessage(request.getId(), isPinned, operatorIdCard);
//            return result ? R.ok(true) : R.fail("置顶操作失败");
//        } catch (Exception e) {
//            log.error("置顶操作失败", e);
//            return R.fail("置顶操作失败：" + e.getMessage());
//        }
//    }

    /**
     * 获取留言详情
     */
    @PostMapping("/detail")
    @ApiOperation("获取留言详情")
    public R<PoliceClubMessage> getMessageDetail(@RequestBody PoliceClubMessage request) {
        try {
            String currentUserIdCard = UserUtils.getUser().getIdCard();
            PoliceClubMessage message = policeClubMessageService.getMessageDetail(request.getId(), currentUserIdCard);
            return message != null ? R.ok(message) : R.fail("留言不存在");
        } catch (Exception e) {
            log.error("获取留言详情失败", e);
            return R.fail("获取留言详情失败：" + e.getMessage());
        }
    }

    /**
     * 查询主留言列表（不包含回复）
     */
    @PostMapping("/mainMessages")
    @ApiOperation("查询主留言列表")
    public R<List<PoliceClubMessage>> getMainMessages(@RequestBody MessageQueryDTO dto) {
        try {
            // 只查询主留言（parentId为0）
            dto.setParentId(0L);

            // 设置当前用户身份证号
            if (dto.getCurrentUserIdCard() == null) {
                dto.setCurrentUserIdCard(UserUtils.getUser().getIdCard());
            }

            Page<PoliceClubMessage> page = policeClubMessageService.pageMessages(dto);
            return R.ok(page.getRecords(), (int) page.getTotal());
        } catch (Exception e) {
            log.error("查询主留言列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 查询置顶留言列表
     */
    @PostMapping("/pinnedMessages")
    @ApiOperation("查询置顶留言列表")
    public R<List<PoliceClubMessage>> getPinnedMessages(@RequestBody MessageQueryDTO dto) {
        try {
            // 只查询置顶留言
            dto.setOnlyPinned(true);
            dto.setParentId(0L); // 只查询主留言

            // 设置当前用户身份证号
            if (dto.getCurrentUserIdCard() == null) {
                dto.setCurrentUserIdCard(UserUtils.getUser().getIdCard());
            }

            Page<PoliceClubMessage> page = policeClubMessageService.pageMessages(dto);
            return R.ok(page.getRecords(), (int) page.getTotal());
        } catch (Exception e) {
            log.error("查询置顶留言列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }
}
