package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfAddDTO;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfQueryDTO;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfReturnDTO;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfUpdateDTO;
import com.hl.archive.enums.TagTypeEnum;
import com.hl.archive.mapper.PoliceTagInfoXjdfMapper;
import com.hl.archive.utils.SsoCacheUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceTagInfoXjdf;
@Service
public class PoliceTagInfoXjdfService extends ServiceImpl<PoliceTagInfoXjdfMapper, PoliceTagInfoXjdf> {

    public boolean addTagData(PoliceTagInfoXjdfAddDTO dto) {

        List<String> idCardList = dto.getIdCardList();

        List<PoliceTagInfoXjdf> tagInfoDfList = new ArrayList<>();
        for (String s : idCardList) {
            PoliceTagInfoXjdf tagInfoDf = new PoliceTagInfoXjdf();
            tagInfoDf.setIdCard(s);
            String organizationId = SsoCacheUtil.getUserOrgIdByIdCard(s);
            tagInfoDf.setOrganizationId(organizationId);
            tagInfoDf.setTagType(dto.getTagType());
            tagInfoDf.setTagName(dto.getTagName());
            tagInfoDf.setAwardDate(dto.getAwardDate());
            tagInfoDf.setRemark(dto.getRemark());
            tagInfoDfList.add(tagInfoDf);
        }
        boolean b = this.saveBatch(tagInfoDfList);
        return b;
    }

    public Page<PoliceTagInfoXjdfReturnDTO> listTag(PoliceTagInfoXjdfQueryDTO dto) {
        Page<PoliceTagInfoXjdfReturnDTO> objectPage = Page.of(dto.getPage(), dto.getLimit());
        String organizationId = dto.getOrganizationId();
        if (StrUtil.isNotBlank(organizationId)) {
            if ("320412000000".equals(organizationId)) {
                dto.setOrganizationId(null);
            }else {
                dto.setOrganizationId(organizationId.substring(0, 8));
            }
        }
        Page<PoliceTagInfoXjdfReturnDTO> page = this.baseMapper.listTag(objectPage,dto);
        return page;
    }

    /**
     * 修改标签数据
     * @param dto 修改数据DTO
     * @return 修改结果
     */
    public boolean updateTagData(PoliceTagInfoXjdfUpdateDTO dto) {
        if (dto == null || dto.getIds() == null || dto.getIds().isEmpty()) {
            throw new IllegalArgumentException("修改的记录ID不能为空");
        }

        // 验证标签类型
        if (StrUtil.isNotBlank(dto.getTagType()) && !TagTypeEnum.isValidCode(dto.getTagType())) {
            throw new IllegalArgumentException("无效的标签类型: " + dto.getTagType());
        }

        // 查询要修改的记录
        List<PoliceTagInfoXjdf> existingRecords = this.listByIds(dto.getIds());
        if (existingRecords.isEmpty()) {
            throw new IllegalArgumentException("未找到要修改的记录");
        }

        // 更新记录
        List<PoliceTagInfoXjdf> updateList = new ArrayList<>();
        for (PoliceTagInfoXjdf record : existingRecords) {
            // 只更新非空字段
            if (StrUtil.isNotBlank(dto.getTagType())) {
                record.setTagType(dto.getTagType());
            }
            if (StrUtil.isNotBlank(dto.getTagName())) {
                record.setTagName(dto.getTagName());
            }
            if (dto.getAwardDate() != null) {
                record.setAwardDate(dto.getAwardDate());
            }
            if (StrUtil.isNotBlank(dto.getRemark())) {
                record.setRemark(dto.getRemark());
            }
            updateList.add(record);
        }

        return this.updateBatchById(updateList);
    }
}
