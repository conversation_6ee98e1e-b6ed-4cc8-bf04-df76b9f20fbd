package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.AuxiliaryPoliceInfoQueryDTO;
import com.hl.archive.utils.SsoCacheUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.mapper.AuxiliaryPoliceInfoMapper;
import com.hl.archive.domain.entity.AuxiliaryPoliceInfo;
@Service
public class AuxiliaryPoliceInfoService extends ServiceImpl<AuxiliaryPoliceInfoMapper, AuxiliaryPoliceInfo> {

    public Page<AuxiliaryPoliceInfo> pageList(AuxiliaryPoliceInfoQueryDTO dto) {
        LambdaQueryWrapper<AuxiliaryPoliceInfo> queryWrapper = Wrappers.<AuxiliaryPoliceInfo>lambdaQuery();
        if (StrUtil.isNotBlank(dto.getOrganizationId())) {
            if (!"320412000000".equals(dto.getOrganizationId())) {
                String organizationName = SsoCacheUtil.getOrganizationName(dto.getOrganizationId());
                organizationName = organizationName.replaceAll("派出所", "")
                        .replaceAll("武进分局", "");
                queryWrapper.like(AuxiliaryPoliceInfo::getOrganization, organizationName);
            }
        }

        if (StrUtil.isNotBlank(dto.getQuery())){
            queryWrapper.and(w-> w.like(AuxiliaryPoliceInfo::getName, dto.getQuery())
                    .or()
                    .like(AuxiliaryPoliceInfo::getIdCard, dto.getQuery())
                    .or()
                    .like(AuxiliaryPoliceInfo::getOrganization, dto.getQuery()));
        }

        Page<AuxiliaryPoliceInfo> auxiliaryPoliceInfoPage = page(Page.of(dto.getPage(), dto.getLimit()), queryWrapper);
        return auxiliaryPoliceInfoPage;
    }
}
