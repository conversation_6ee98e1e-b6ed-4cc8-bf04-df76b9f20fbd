package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.MessageAddDTO;
import com.hl.archive.domain.dto.MessageLikeDTO;
import com.hl.archive.domain.dto.MessageQueryDTO;
import com.hl.archive.domain.entity.PoliceClubMessage;
import com.hl.archive.domain.entity.PoliceClubMessageLike;
import com.hl.archive.mapper.PoliceClubMessageMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 社团留言服务类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class PoliceClubMessageService extends ServiceImpl<PoliceClubMessageMapper, PoliceClubMessage> {

    private final PoliceClubMessageLikeService policeClubMessageLikeService;

    /**
     * 分页查询留言列表
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    public Page<PoliceClubMessage> pageMessages(MessageQueryDTO dto) {
        Page<PoliceClubMessage> page = Page.of(dto.getPage(), dto.getLimit());
        
        // 构建查询条件
        LambdaQueryWrapper<PoliceClubMessage> wrapper = Wrappers.<PoliceClubMessage>lambdaQuery()
                .eq(dto.getClubId() != null, PoliceClubMessage::getClubId, dto.getClubId())
                .eq(dto.getParentId() != null, PoliceClubMessage::getParentId, dto.getParentId())
                .eq(dto.getRootId() != null && dto.getRootId() > 0, PoliceClubMessage::getRootId, dto.getRootId())
                .eq(dto.getAuthorIdCard() != null, PoliceClubMessage::getAuthorIdCard, dto.getAuthorIdCard())
                .eq(dto.getMessageType() != null, PoliceClubMessage::getMessageType, dto.getMessageType())
                .eq(dto.getStatus() != null, PoliceClubMessage::getStatus, dto.getStatus())
                .eq(Boolean.TRUE.equals(dto.getOnlyPinned()), PoliceClubMessage::getIsPinned, 1)
                .like(StrUtil.isNotBlank(dto.getKeyword()), PoliceClubMessage::getContent, dto.getKeyword())
                .eq(PoliceClubMessage::getStatus, 1); // 只查询正常状态的留言

        // 排序：置顶留言优先，然后按指定字段排序
        if ("created_at".equals(dto.getOrderBy())) {
            wrapper.orderByDesc(PoliceClubMessage::getIsPinned)
                   .orderByDesc("DESC".equals(dto.getOrderDirection()), PoliceClubMessage::getCreatedAt)
                   .orderByAsc("ASC".equals(dto.getOrderDirection()), PoliceClubMessage::getCreatedAt);
        } else {
            wrapper.orderByDesc(PoliceClubMessage::getIsPinned)
                   .orderByDesc(PoliceClubMessage::getCreatedAt);
        }

        Page<PoliceClubMessage> result = this.page(page, wrapper);
        
        // 设置点赞状态
        if (StrUtil.isNotBlank(dto.getCurrentUserIdCard()) && !result.getRecords().isEmpty()) {
            setLikeStatus(result.getRecords(), dto.getCurrentUserIdCard());
        }
        
        // 加载回复列表
        if (Boolean.TRUE.equals(dto.getIncludeReplies()) && !result.getRecords().isEmpty()) {
            loadReplies(result.getRecords(), dto.getCurrentUserIdCard(), dto.getReplyLimit());
        }

        return result;
    }

    /**
     * 添加留言
     *
     * @param dto 留言信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addMessage(MessageAddDTO dto) {
        PoliceClubMessage message = new PoliceClubMessage();
        message.setClubId(dto.getClubId());
        message.setParentId(dto.getParentId());
        message.setRootId(dto.getRootId());
        message.setContent(dto.getContent());
        message.setAuthorIdCard(dto.getAuthorIdCard());
        message.setReplyToIdCard(dto.getReplyToIdCard());
        message.setMessageType(dto.getMessageType());
        message.setIsPinned(0);
        message.setLikeCount(0);
        message.setReplyCount(0);
        message.setStatus(1);
        message.setCreatedAt(LocalDateTime.now());
        message.setUpdatedAt(LocalDateTime.now());
        message.setCreatedBy(dto.getAuthorIdCard());
        message.setUpdatedBy(dto.getAuthorIdCard());
        message.setIsDeleted(0);

        // 如果是回复，设置根留言ID
        if (dto.getParentId() != null && dto.getParentId() > 0) {
            PoliceClubMessage parentMessage = this.getById(dto.getParentId());
            if (parentMessage != null) {
                // 如果父留言的rootId为0，说明父留言就是根留言
                message.setRootId(parentMessage.getRootId() > 0 ? parentMessage.getRootId() : parentMessage.getId());

                // 更新父留言的回复数
                this.baseMapper.updateReplyCount(dto.getParentId(), 1);

                // 如果有根留言且不是直接回复根留言，也要更新根留言的回复数
                if (parentMessage.getRootId() > 0 && !parentMessage.getRootId().equals(dto.getParentId())) {
                    this.baseMapper.updateReplyCount(parentMessage.getRootId(), 1);
                }
            }
        }

        return this.save(message);
    }

    /**
     * 回复留言
     *
     * @param dto 回复信息
     * @return 是否成功
     */
    public boolean replyMessage(MessageAddDTO dto) {
        if (dto.getParentId() == null || dto.getParentId() <= 0) {
            throw new IllegalArgumentException("回复留言时父留言ID不能为空或0");
        }
        return addMessage(dto);
    }

    /**
     * 获取留言的回复列表
     *
     * @param dto 查询条件
     * @return 回复列表
     */
    public List<PoliceClubMessage> getReplies(MessageQueryDTO dto) {
        if (dto.getParentId() == null || dto.getParentId() <= 0) {
            throw new IllegalArgumentException("查询回复时父留言ID不能为空或0");
        }

        List<PoliceClubMessage> replies = this.baseMapper.selectRepliesByParentId(
                dto.getParentId(), dto.getCurrentUserIdCard(), dto.getLimit());
        
        // 设置点赞状态
        if (StrUtil.isNotBlank(dto.getCurrentUserIdCard()) && !replies.isEmpty()) {
            setLikeStatus(replies, dto.getCurrentUserIdCard());
        }

        return replies;
    }

    /**
     * 点赞/取消点赞留言
     *
     * @param dto 点赞信息
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean likeMessage(MessageLikeDTO dto) {
        PoliceClubMessageLike existingLike = policeClubMessageLikeService.getOne(
                Wrappers.<PoliceClubMessageLike>lambdaQuery()
                        .eq(PoliceClubMessageLike::getMessageId, dto.getMessageId())
                        .eq(PoliceClubMessageLike::getUserIdCard, dto.getUserIdCard())
        );

        if (Boolean.TRUE.equals(dto.getIsLike())) {
            // 点赞
            if (existingLike == null) {
                PoliceClubMessageLike like = new PoliceClubMessageLike();
                like.setMessageId(dto.getMessageId());
                like.setUserIdCard(dto.getUserIdCard());
                like.setCreatedAt(LocalDateTime.now());
                
                policeClubMessageLikeService.save(like);
                this.baseMapper.updateLikeCount(dto.getMessageId(), 1);
            }
        } else {
            // 取消点赞
            if (existingLike != null) {
                policeClubMessageLikeService.removeById(existingLike.getId());
                this.baseMapper.updateLikeCount(dto.getMessageId(), -1);
            }
        }

        return true;
    }

    /**
     * 删除留言
     *
     * @param messageId 留言ID
     * @param operatorIdCard 操作人身份证号
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMessage(Long messageId, String operatorIdCard) {
        PoliceClubMessage message = this.getById(messageId);
        if (message == null) {
            return false;
        }

        // 逻辑删除
        message.setStatus(0);
        message.setUpdatedAt(LocalDateTime.now());
        message.setUpdatedBy(operatorIdCard);
        
        boolean result = this.updateById(message);
        
        // 如果是回复，需要更新父留言的回复数
        if (result && message.getParentId() != null && message.getParentId() > 0) {
            this.baseMapper.updateReplyCount(message.getParentId(), -1);

            // 如果有根留言，也要更新根留言的回复数
            if (message.getRootId() != null && message.getRootId() > 0 && !message.getRootId().equals(message.getParentId())) {
                this.baseMapper.updateReplyCount(message.getRootId(), -1);
            }
        }

        return result;
    }

    /**
     * 置顶/取消置顶留言
     *
     * @param messageId 留言ID
     * @param isPinned 是否置顶
     * @param operatorIdCard 操作人身份证号
     * @return 是否成功
     */
    public boolean pinMessage(Long messageId, boolean isPinned, String operatorIdCard) {
        PoliceClubMessage message = this.getById(messageId);
        if (message == null) {
            return false;
        }

        message.setIsPinned(isPinned ? 1 : 0);
        message.setUpdatedAt(LocalDateTime.now());
        message.setUpdatedBy(operatorIdCard);

        return this.updateById(message);
    }

    /**
     * 获取留言详情
     *
     * @param messageId 留言ID
     * @param currentUserIdCard 当前用户身份证号
     * @return 留言详情
     */
    public PoliceClubMessage getMessageDetail(Long messageId, String currentUserIdCard) {
        return this.baseMapper.selectMessageDetail(messageId, currentUserIdCard);
    }

    /**
     * 设置留言列表的点赞状态
     *
     * @param messages 留言列表
     * @param userIdCard 用户身份证号
     */
    private void setLikeStatus(List<PoliceClubMessage> messages, String userIdCard) {
        if (messages.isEmpty() || StrUtil.isBlank(userIdCard)) {
            return;
        }

        List<Long> messageIds = messages.stream()
                .map(PoliceClubMessage::getId)
                .collect(Collectors.toList());

        List<Long> likedMessageIds = policeClubMessageLikeService.getUserLikedMessageIds(messageIds, userIdCard);
        
        messages.forEach(message -> 
                message.setIsLiked(likedMessageIds.contains(message.getId())));
    }

    /**
     * 加载留言的回复列表
     *
     * @param messages 留言列表
     * @param currentUserIdCard 当前用户身份证号
     * @param replyLimit 回复限制数量
     */
    private void loadReplies(List<PoliceClubMessage> messages, String currentUserIdCard, Integer replyLimit) {
        for (PoliceClubMessage message : messages) {
            if (message.getReplyCount() != null && message.getReplyCount() > 0) {
                List<PoliceClubMessage> replies = this.baseMapper.selectRepliesByParentId(
                        message.getId(), currentUserIdCard, replyLimit);
                
                if (StrUtil.isNotBlank(currentUserIdCard) && !replies.isEmpty()) {
                    setLikeStatus(replies, currentUserIdCard);
                }
                
                message.setReplies(replies);
            }
        }
    }
}
