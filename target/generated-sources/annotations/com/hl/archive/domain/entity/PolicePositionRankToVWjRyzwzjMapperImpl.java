package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyzwzj;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T15:00:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PolicePositionRankToVWjRyzwzjMapperImpl implements PolicePositionRankToVWjRyzwzjMapper {

    @Override
    public VWjRyzwzj convert(PolicePositionRank source) {
        if ( source == null ) {
            return null;
        }

        VWjRyzwzj vWjRyzwzj = new VWjRyzwzj();

        vWjRyzwzj.setGmsfhm( source.getIdCard() );
        vWjRyzwzj.setZwmc( source.getPositionName() );
        if ( source.getCurrentRankDate() != null ) {
            vWjRyzwzj.setXzjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getCurrentRankDate() ) );
        }
        if ( source.getCurrentPositionDate() != null ) {
            vWjRyzwzj.setZwsxsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getCurrentPositionDate() ) );
        }
        vWjRyzwzj.setRzwh( source.getAppointmentDocument() );
        vWjRyzwzj.setGazwjb( source.getPolicePositionLevel() );

        return vWjRyzwzj;
    }

    @Override
    public VWjRyzwzj convert(PolicePositionRank source, VWjRyzwzj target) {
        if ( source == null ) {
            return target;
        }

        target.setGmsfhm( source.getIdCard() );
        target.setZwmc( source.getPositionName() );
        if ( source.getCurrentRankDate() != null ) {
            target.setXzjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getCurrentRankDate() ) );
        }
        else {
            target.setXzjsj( null );
        }
        if ( source.getCurrentPositionDate() != null ) {
            target.setZwsxsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getCurrentPositionDate() ) );
        }
        else {
            target.setZwsxsj( null );
        }
        target.setRzwh( source.getAppointmentDocument() );
        target.setGazwjb( source.getPolicePositionLevel() );

        return target;
    }
}
