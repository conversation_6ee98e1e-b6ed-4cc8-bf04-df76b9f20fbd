package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyndkh;
import com.hl.orasync.domain.VWjRyndkhToPoliceAnnualAssessmentMapper;
import io.github.linpeilie.AutoMapperConfig__437;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__437.class,
    uses = {VWjRyndkhToPoliceAnnualAssessmentMapper.class},
    imports = {}
)
public interface PoliceAnnualAssessmentToVWjRyndkhMapper extends BaseMapper<PoliceAnnualAssessment, VWjRyndkh> {
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "khjg",
      source = "assessmentResult"
  )
  @Mapping(
      target = "kcnd",
      source = "assessmentYear"
  )
  @Mapping(
      target = "kclb",
      source = "assessmentCategory"
  )
  VWjRyndkh convert(PoliceAnnualAssessment source);

  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "khjg",
      source = "assessmentResult"
  )
  @Mapping(
      target = "kcnd",
      source = "assessmentYear"
  )
  @Mapping(
      target = "kclb",
      source = "assessmentCategory"
  )
  VWjRyndkh convert(PoliceAnnualAssessment source, @MappingTarget VWjRyndkh target);
}
