package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrHyzk;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T15:00:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceMarriageStatusToVWjBrHyzkMapperImpl implements PoliceMarriageStatusToVWjBrHyzkMapper {

    @Override
    public VWjBrHyzk convert(PoliceMarriageStatus source) {
        if ( source == null ) {
            return null;
        }

        VWjBrHyzk vWjBrHyzk = new VWjBrHyzk();

        vWjBrHyzk.setBhhyztmc( source.getChangeStatus() );
        if ( source.getChangeDate() != null ) {
            vWjBrHyzk.setBhsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getChangeDate() ) );
        }
        vWjBrHyzk.setXm( source.getName() );
        vWjBrHyzk.setGmsfhm( source.getIdCard() );
        vWjBrHyzk.setHyztmc( source.getMarriageStatus() );

        return vWjBrHyzk;
    }

    @Override
    public VWjBrHyzk convert(PoliceMarriageStatus source, VWjBrHyzk target) {
        if ( source == null ) {
            return target;
        }

        target.setBhhyztmc( source.getChangeStatus() );
        if ( source.getChangeDate() != null ) {
            target.setBhsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getChangeDate() ) );
        }
        else {
            target.setBhsj( null );
        }
        target.setXm( source.getName() );
        target.setGmsfhm( source.getIdCard() );
        target.setHyztmc( source.getMarriageStatus() );

        return target;
    }
}
