package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrHyzk;
import com.hl.orasync.domain.VWjBrHyzkToPoliceMarriageStatusMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__437;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__437.class,
    uses = {ConversionUtils.class,VWjBrHyzkToPoliceMarriageStatusMapper.class},
    imports = {}
)
public interface PoliceMarriageStatusToVWjBrHyzkMapper extends BaseMapper<PoliceMarriageStatus, VWjBrHyzk> {
  @Mapping(
      target = "bhhyztmc",
      source = "changeStatus"
  )
  @Mapping(
      target = "bhsj",
      source = "changeDate"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "hyztmc",
      source = "marriageStatus"
  )
  VWjBrHyzk convert(PoliceMarriageStatus source);

  @Mapping(
      target = "bhhyztmc",
      source = "changeStatus"
  )
  @Mapping(
      target = "bhsj",
      source = "changeDate"
  )
  @Mapping(
      target = "xm",
      source = "name"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "hyztmc",
      source = "marriageStatus"
  )
  VWjBrHyzk convert(PoliceMarriageStatus source, @MappingTarget VWjBrHyzk target);
}
