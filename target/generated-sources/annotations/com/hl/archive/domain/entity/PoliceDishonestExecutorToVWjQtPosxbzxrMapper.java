package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtPosxbzxr;
import com.hl.orasync.domain.VWjQtPosxbzxrToPoliceDishonestExecutorMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__437;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__437.class,
    uses = {ConversionUtils.class,VWjQtPosxbzxrToPoliceDishonestExecutorMapper.class},
    imports = {}
)
public interface PoliceDishonestExecutorToVWjQtPosxbzxrMapper extends BaseMapper<PoliceDishonestExecutor, VWjQtPosxbzxr> {
  @Mapping(
      target = "zxjg",
      source = "executionUnit"
  )
  @Mapping(
      target = "jtqk",
      source = "dishonestReason"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "rygxmc",
      source = "relationship"
  )
  VWjQtPosxbzxr convert(PoliceDishonestExecutor source);

  @Mapping(
      target = "zxjg",
      source = "executionUnit"
  )
  @Mapping(
      target = "jtqk",
      source = "dishonestReason"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "rygxmc",
      source = "relationship"
  )
  VWjQtPosxbzxr convert(PoliceDishonestExecutor source, @MappingTarget VWjQtPosxbzxr target);
}
