package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjxxx;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T15:00:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceRankInfoToVWjRyjxxxMapperImpl implements PoliceRankInfoToVWjRyjxxxMapper {

    @Override
    public VWjRyjxxx convert(PoliceRankInfo source) {
        if ( source == null ) {
            return null;
        }

        VWjRyjxxx vWjRyjxxx = new VWjRyjxxx();

        if ( source.getRankStartDate() != null ) {
            vWjRyjxxx.setXcqsrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRankStartDate() ) );
        }
        vWjRyjxxx.setSxzl( source.getRankType() );
        vWjRyjxxx.setSxyy( source.getPromotionReason() );
        vWjRyjxxx.setGmsfhm( source.getIdCard() );
        if ( source.getPromotionDate() != null ) {
            vWjRyjxxx.setSxsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getPromotionDate() ) );
        }
        vWjRyjxxx.setXc( source.getRankTitle() );
        if ( source.getRankEndDate() != null ) {
            vWjRyjxxx.setXczzrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRankEndDate() ) );
        }
        vWjRyjxxx.setSxsxzzj( source.getAdminLevelAtPromotion() );

        return vWjRyjxxx;
    }

    @Override
    public VWjRyjxxx convert(PoliceRankInfo source, VWjRyjxxx target) {
        if ( source == null ) {
            return target;
        }

        if ( source.getRankStartDate() != null ) {
            target.setXcqsrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRankStartDate() ) );
        }
        else {
            target.setXcqsrq( null );
        }
        target.setSxzl( source.getRankType() );
        target.setSxyy( source.getPromotionReason() );
        target.setGmsfhm( source.getIdCard() );
        if ( source.getPromotionDate() != null ) {
            target.setSxsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getPromotionDate() ) );
        }
        else {
            target.setSxsj( null );
        }
        target.setXc( source.getRankTitle() );
        if ( source.getRankEndDate() != null ) {
            target.setXczzrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRankEndDate() ) );
        }
        else {
            target.setXczzrq( null );
        }
        target.setSxsxzzj( source.getAdminLevelAtPromotion() );

        return target;
    }
}
