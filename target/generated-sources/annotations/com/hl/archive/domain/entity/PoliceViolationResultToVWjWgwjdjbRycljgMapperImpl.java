package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjWgwjdjbRycljg;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T15:00:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceViolationResultToVWjWgwjdjbRycljgMapperImpl implements PoliceViolationResultToVWjWgwjdjbRycljgMapper {

    @Override
    public VWjWgwjdjbRycljg convert(PoliceViolationResult source) {
        if ( source == null ) {
            return null;
        }

        VWjWgwjdjbRycljg vWjWgwjdjbRycljg = new VWjWgwjdjbRycljg();

        vWjWgwjdjbRycljg.setCldw( source.getCldw() );
        vWjWgwjdjbRycljg.setCljg( source.getCljg() );
        vWjWgwjdjbRycljg.setRyXxzjbh( source.getRyXxzjbh() );
        vWjWgwjdjbRycljg.setWtXxzjbh( source.getWtXxzjbh() );
        vWjWgwjdjbRycljg.setLbmc( source.getLbmc() );
        if ( source.getClsj() != null ) {
            vWjWgwjdjbRycljg.setClsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getClsj() ) );
        }
        vWjWgwjdjbRycljg.setXxzjbh( source.getXxzjbh() );

        return vWjWgwjdjbRycljg;
    }

    @Override
    public VWjWgwjdjbRycljg convert(PoliceViolationResult source, VWjWgwjdjbRycljg target) {
        if ( source == null ) {
            return target;
        }

        target.setCldw( source.getCldw() );
        target.setCljg( source.getCljg() );
        target.setRyXxzjbh( source.getRyXxzjbh() );
        target.setWtXxzjbh( source.getWtXxzjbh() );
        target.setLbmc( source.getLbmc() );
        if ( source.getClsj() != null ) {
            target.setClsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getClsj() ) );
        }
        else {
            target.setClsj( null );
        }
        target.setXxzjbh( source.getXxzjbh() );

        return target;
    }
}
