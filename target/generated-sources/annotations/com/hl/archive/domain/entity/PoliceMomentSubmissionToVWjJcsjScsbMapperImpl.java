package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjJcsjScsb;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T15:00:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceMomentSubmissionToVWjJcsjScsbMapperImpl implements PoliceMomentSubmissionToVWjJcsjScsbMapper {

    @Override
    public VWjJcsjScsb convert(PoliceMomentSubmission source) {
        if ( source == null ) {
            return null;
        }

        VWjJcsjScsb vWjJcsjScsb = new VWjJcsjScsb();

        vWjJcsjScsb.setShjgmc( source.getAuditResult() );
        vWjJcsjScsb.setBslxmc( source.getSubmissionType() );
        if ( source.getSubmissionTime() != null ) {
            vWjJcsjScsb.setDjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getSubmissionTime() ) );
        }
        vWjJcsjScsb.setBsdwmc( source.getSubmitUnit() );
        vWjJcsjScsb.setScjj( source.getMaterialIntro() );
        vWjJcsjScsb.setCjmj( source.getOfficerName() );
        vWjJcsjScsb.setBsrxm( source.getSubmitter() );
        vWjJcsjScsb.setXxzjbh( source.getZjbh() );
        vWjJcsjScsb.setCllxmc( source.getMaterialType() );

        return vWjJcsjScsb;
    }

    @Override
    public VWjJcsjScsb convert(PoliceMomentSubmission source, VWjJcsjScsb target) {
        if ( source == null ) {
            return target;
        }

        target.setShjgmc( source.getAuditResult() );
        target.setBslxmc( source.getSubmissionType() );
        if ( source.getSubmissionTime() != null ) {
            target.setDjsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getSubmissionTime() ) );
        }
        else {
            target.setDjsj( null );
        }
        target.setBsdwmc( source.getSubmitUnit() );
        target.setScjj( source.getMaterialIntro() );
        target.setCjmj( source.getOfficerName() );
        target.setBsrxm( source.getSubmitter() );
        target.setXxzjbh( source.getZjbh() );
        target.setCllxmc( source.getMaterialType() );

        return target;
    }
}
