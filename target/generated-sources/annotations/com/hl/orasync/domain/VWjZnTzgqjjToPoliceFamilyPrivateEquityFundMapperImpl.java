package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyPrivateEquityFund;
import com.hl.orasync.util.ConversionUtils;
import java.math.BigDecimal;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-02T15:00:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjZnTzgqjjToPoliceFamilyPrivateEquityFundMapperImpl implements VWjZnTzgqjjToPoliceFamilyPrivateEquityFundMapper {

    @Override
    public PoliceFamilyPrivateEquityFund convert(VWjZnTzgqjj source) {
        if ( source == null ) {
            return null;
        }

        PoliceFamilyPrivateEquityFund policeFamilyPrivateEquityFund = new PoliceFamilyPrivateEquityFund();

        policeFamilyPrivateEquityFund.setFundNameCode( source.getMc() );
        policeFamilyPrivateEquityFund.setContractSigningDate( ConversionUtils.strToDate( source.getQsrq() ) );
        if ( source.getZje() != null ) {
            policeFamilyPrivateEquityFund.setTotalPaidAmount( new BigDecimal( source.getZje() ) );
        }
        policeFamilyPrivateEquityFund.setSubscriptionAmount( ConversionUtils.strToBigDecimal( source.getRjje() ) );
        policeFamilyPrivateEquityFund.setIdCard( source.getGmsfhm() );
        policeFamilyPrivateEquityFund.setContractExpiryDate( ConversionUtils.strToDate( source.getJzrq() ) );
        policeFamilyPrivateEquityFund.setName( source.getXmFr() );
        policeFamilyPrivateEquityFund.setFundInvestmentDirection( source.getJjtx() );
        policeFamilyPrivateEquityFund.setPersonalPaidAmount( ConversionUtils.strToBigDecimal( source.getGrje() ) );

        return policeFamilyPrivateEquityFund;
    }

    @Override
    public PoliceFamilyPrivateEquityFund convert(VWjZnTzgqjj source, PoliceFamilyPrivateEquityFund target) {
        if ( source == null ) {
            return target;
        }

        target.setFundNameCode( source.getMc() );
        target.setContractSigningDate( ConversionUtils.strToDate( source.getQsrq() ) );
        if ( source.getZje() != null ) {
            target.setTotalPaidAmount( new BigDecimal( source.getZje() ) );
        }
        else {
            target.setTotalPaidAmount( null );
        }
        target.setSubscriptionAmount( ConversionUtils.strToBigDecimal( source.getRjje() ) );
        target.setIdCard( source.getGmsfhm() );
        target.setContractExpiryDate( ConversionUtils.strToDate( source.getJzrq() ) );
        target.setName( source.getXmFr() );
        target.setFundInvestmentDirection( source.getJjtx() );
        target.setPersonalPaidAmount( ConversionUtils.strToBigDecimal( source.getGrje() ) );

        return target;
    }
}
