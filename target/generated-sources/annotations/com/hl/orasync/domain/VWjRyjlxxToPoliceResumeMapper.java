package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceResume;
import com.hl.archive.domain.entity.PoliceResumeToVWjRyjlxxMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__437;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__437.class,
    uses = {ConversionUtils.class,PoliceResumeToVWjRyjlxxMapper.class},
    imports = {}
)
public interface VWjRyjlxxToPoliceResumeMapper extends BaseMapper<VWjRyjlxx, PoliceResume> {
  @Mapping(
      target = "workUnit",
      source = "szdw"
  )
  @Mapping(
      target = "endDate",
      source = "jzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "position",
      source = "zw"
  )
  @Mapping(
      target = "startDate",
      source = "qssj",
      qualifiedByName = {"strToDate"}
  )
  PoliceResume convert(VWjRyjlxx source);

  @Mapping(
      target = "workUnit",
      source = "szdw"
  )
  @Mapping(
      target = "endDate",
      source = "jzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "position",
      source = "zw"
  )
  @Mapping(
      target = "startDate",
      source = "qssj",
      qualifiedByName = {"strToDate"}
  )
  PoliceResume convert(VWjRyjlxx source, @MappingTarget PoliceResume target);
}
