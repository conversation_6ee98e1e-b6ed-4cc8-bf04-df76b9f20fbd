-- 社团留言功能相关表结构

-- 1. 社团留言表
CREATE TABLE `police_club_message` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `club_id` BIGINT NOT NULL COMMENT '所属社团ID',
    `parent_id` BIGINT DEFAULT 0 COMMENT '父留言ID（0表示主留言，非0表示回复）',
    `root_id` BIGINT DEFAULT 0 COMMENT '根留言ID（用于快速查找整个讨论串）',
    `content` TEXT NOT NULL COMMENT '留言内容',
    `author_id_card` VARCHAR(18) NOT NULL COMMENT '留言作者身份证号',
    `reply_to_id_card` VARCHAR(18) DEFAULT NULL COMMENT '回复目标用户身份证号',
    `message_type` TINYINT DEFAULT 1 COMMENT '留言类型（1=普通留言，2=置顶留言，3=公告）',
    `is_pinned` TINYINT DEFAULT 0 COMMENT '是否置顶（0=否，1=是）',
    `like_count` INT DEFAULT 0 COMMENT '点赞数',
    `reply_count` INT DEFAULT 0 COMMENT '回复数',
    `status` TINYINT DEFAULT 1 COMMENT '状态（0=已删除，1=正常，2=待审核）',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` VARCHAR(18) COMMENT '创建人',
    `updated_by` VARCHAR(18) COMMENT '更新人',
    `is_deleted` TINYINT DEFAULT 0 COMMENT '逻辑删除（0=未删除，1=已删除）',
    
    INDEX `idx_club_id` (`club_id`),
    INDEX `idx_parent_id` (`parent_id`),
    INDEX `idx_root_id` (`root_id`),
    INDEX `idx_author` (`author_id_card`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_status` (`status`),
    INDEX `idx_pinned` (`is_pinned`),
    INDEX `idx_club_parent` (`club_id`, `parent_id`),
    INDEX `idx_club_status_pinned` (`club_id`, `status`, `is_pinned`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社团留言表';

-- 2. 留言点赞表
CREATE TABLE `police_club_message_like` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    `message_id` BIGINT NOT NULL COMMENT '留言ID',
    `user_id_card` VARCHAR(18) NOT NULL COMMENT '点赞用户身份证号',
    `created_at` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
    
    UNIQUE KEY `uk_message_user` (`message_id`, `user_id_card`),
    INDEX `idx_message_id` (`message_id`),
    INDEX `idx_user_id_card` (`user_id_card`),
    
    CONSTRAINT `fk_message_like_message` FOREIGN KEY (`message_id`) REFERENCES `police_club_message` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='留言点赞表';

-- 插入示例数据（可选）
-- 注意：需要先确保 police_club_info 表中有对应的社团数据

-- 示例主留言
INSERT INTO `police_club_message` (
    `club_id`, `parent_id`, `root_id`, `content`, `author_id_card`, 
    `message_type`, `is_pinned`, `like_count`, `reply_count`, `status`,
    `created_by`, `updated_by`
) VALUES 
(1, 0, 0, '欢迎大家加入我们的社团！这里是一个交流学习的好地方。', '320412199001011234', 
 2, 1, 5, 3, 1, '320412199001011234', '320412199001011234'),

(1, 0, 0, '今天的活动很精彩，感谢大家的参与！', '320412199002022345', 
 1, 0, 8, 2, 1, '320412199002022345', '320412199002022345'),

(1, 0, 0, '下次活动什么时候举办？期待中...', '320412199003033456', 
 1, 0, 3, 1, 1, '320412199003033456', '320412199003033456');

-- 示例回复留言
INSERT INTO `police_club_message` (
    `club_id`, `parent_id`, `root_id`, `content`, `author_id_card`, `reply_to_id_card`,
    `message_type`, `is_pinned`, `like_count`, `reply_count`, `status`,
    `created_by`, `updated_by`
) VALUES 
(1, 1, 1, '谢谢社长的欢迎！', '320412199004044567', '320412199001011234',
 1, 0, 2, 0, 1, '320412199004044567', '320412199004044567'),

(1, 1, 1, '希望能在这里学到更多知识。', '320412199005055678', '320412199001011234',
 1, 0, 1, 0, 1, '320412199005055678', '320412199005055678'),

(1, 2, 2, '确实很棒，下次还要参加！', '320412199006066789', '320412199002022345',
 1, 0, 4, 0, 1, '320412199006066789', '320412199006066789');

-- 示例点赞数据
INSERT INTO `police_club_message_like` (`message_id`, `user_id_card`) VALUES 
(1, '320412199004044567'),
(1, '320412199005055678'),
(1, '320412199006066789'),
(2, '320412199001011234'),
(2, '320412199003033456'),
(3, '320412199002022345');

-- 更新留言的统计数据（确保数据一致性）
UPDATE `police_club_message` SET 
    `like_count` = (SELECT COUNT(*) FROM `police_club_message_like` WHERE `message_id` = `police_club_message`.`id`),
    `reply_count` = (SELECT COUNT(*) FROM `police_club_message` AS replies WHERE replies.`parent_id` = `police_club_message`.`id` AND replies.`status` = 1)
WHERE `id` IN (1, 2, 3);
